package ai.conrain.aigc.platform.integration.javacv;


import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;

@Slf4j
@Service
public class JavaCVService {

    /**
     * 从视频中按间隔抽帧并保存
     *
     * @param videoFilePath 视频文件路径
     * @param outputDir     输出目录
     * @param intervalFrames 抽帧间隔（每隔多少帧抽取一帧）
     * @throws Exception 可能抛出异常
     */
    public void extractFramesByFrame(String videoFilePath, String outputDir, int intervalFrames) throws Exception {
        // 创建输出目录
        File outputDirectory = new File(outputDir);
        if (!outputDirectory.exists() && !outputDirectory.mkdirs()) {
            throw new IOException("无法创建输出目录: " + outputDir);
        }

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoFilePath);
                Java2DFrameConverter converter = new Java2DFrameConverter()) {
            // 启动抓取器
            grabber.start();
            // 获取视频信息
            int totalFrames = grabber.getLengthInFrames(); // 总帧数
            double frameRate = grabber.getFrameRate(); // 帧率
            int savedCount = 0;

            Frame frame;
            // 逐帧处理视频
            for (int frameIndex = 0; frameIndex < totalFrames; frameIndex++) {
                frame = grabber.grabImage(); // 抓取视频帧

                // 按间隔抽帧
                if (frame != null && frame.image != null && frameIndex % intervalFrames == 0) {
                    // 转换帧为BufferedImage
                    BufferedImage image = converter.getBufferedImage(frame);

                    // 生成输出文件名
                    String outputFilePath = String.format("%s/frame_%05d.jpg", outputDir, frameIndex);
                    File outputFile = new File(outputFilePath);

                    // 保存图像
                    ImageIO.write(image, "jpg", outputFile);
                    savedCount++;

                    log.info("extract frames by frame 结果已保存: {}", outputFilePath);
                }
            }
            log.info("extract frames by frame 抽帧完成，共保存 {} 帧图像", savedCount);
        } catch (IOException e ) {
            log.error("Error processing video: {}", e.getMessage());
            Throwable[] suppressedExceptions = e.getSuppressed();
            for (Throwable suppressed : suppressedExceptions) {
                // 处理或记录被抑制的异常，通常是关闭资源时产生的
                log.error("Suppressed exception during close: {}", suppressed.getMessage());
            }
            throw e;
        }
    }

    /**
     * 按固定时间间隔抽取视频帧
     *
     * @param inputVideoPath  输入视频文件路径
     * @param outputDirPath   输出图片的目录路径
     * @param outputPrefix    输出文件名前缀
     * @param format          输出图片格式: png | jpg
     * @param intervalMircoSeconds 抽帧时间间隔（单位：微秒）
     * @throws IOException    可能抛出的异常
     * @throws FFmpegFrameGrabber.Exception 可能抛出的异常
     */
    public void extractFramesByTime(String inputVideoPath, String outputDirPath, String outputPrefix, String format, long intervalMircoSeconds) throws IOException {
        // 创建输出目录
        File outputDir = new File(outputDirPath);
        if (!outputDir.exists() && !outputDir.mkdirs()) {
            throw new IOException("Failed to create output directory: " + outputDirPath);
        }

        try ( FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputVideoPath);
             Java2DFrameConverter converter = new Java2DFrameConverter()) {
            grabber.start();

            // 获取视频总时长（微秒）
            long durationMicroseconds = grabber.getLengthInTime();
            // 转换为 0.1秒
            double durationSeconds = durationMicroseconds / 1_000_000.0;
            log.info("视频总时长 (秒): {}", durationSeconds);

            int savedCount = 0;

            // 从 0 秒开始，每隔 intervalSeconds 微秒抽一帧，直到视频结束
            for (long currentTimeMicroSeconds = 0; currentTimeMicroSeconds < durationMicroseconds; currentTimeMicroSeconds += intervalMircoSeconds) {
                // 跳转
                grabber.setTimestamp(currentTimeMicroSeconds);
                Frame frame = grabber.grabImage();
                // 抓取该时间点的帧
                if (frame != null && frame.image != null) {
                    // 转换帧为 BufferedImage
                    BufferedImage image = converter.convert(frame);
                    // 生成输出文件路径
                    String outputImagePath = String.format("%s/%s_frame_%.1fs_%05d.%s", outputDirPath, outputPrefix, (currentTimeMicroSeconds / 1_000_000.0), savedCount, format);
                    File outputFile = new File(outputImagePath);
                    // 保存图片
                    ImageIO.write(image, format, outputFile);
                    log.info("extract frames by time 结果已保存: {}", outputImagePath);
                    savedCount++;
                }
            }
            log.info("extract frames by time 抽帧完成, 共保存 {} 张图片", savedCount);
        } catch (IOException e ) {
            log.error("Error processing extract frames by time: {}", e.getMessage());
            Throwable[] suppressedExceptions = e.getSuppressed();
            for (Throwable suppressed : suppressedExceptions) {
                // 处理或记录被抑制的异常，通常是关闭资源时产生的
                log.error("Suppressed exception during close while extract frames by time: {}", suppressed.getMessage());
            }
            throw e;
        }
    }

    /**
     * 按固定时间间隔抽取视频帧
     *
     * @param inputVideoPath  输入视频文件路径
     * @param outputDirPath   输出图片的目录路径
     * @param outputPrefix    输出文件名前缀
     * @param intervalSeconds 抽帧时间间隔（单位：秒）
     * @throws IOException    可能抛出的异常
     * @throws FFmpegFrameGrabber.Exception 可能抛出的异常
     */
    public void extractFramesByTime(String inputVideoPath, String outputDirPath, String outputPrefix, String intervalSeconds) throws IOException {
        long intervalMircoSeconds = new BigDecimal(intervalSeconds)
                .multiply(new BigDecimal("1000000"))
                .longValue();
        extractFramesByTime(inputVideoPath, outputDirPath, outputPrefix, "png", intervalMircoSeconds);
    }

}
