package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.LabelingService;
import ai.conrain.aigc.platform.service.component.TrainingSampleService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.vo.training.ExportRequest;
import ai.conrain.aigc.platform.service.model.vo.training.ExportResultVO;
import ai.conrain.aigc.platform.service.model.vo.training.LabelingProgressVO;
import ai.conrain.aigc.platform.service.model.vo.training.LabelingSampleRequest;
import ai.conrain.aigc.platform.service.model.vo.training.LabelingSampleSummary;
import ai.conrain.aigc.platform.service.model.vo.training.SaveLabelsRequest;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 主动学习标注控制器
 * 基于现有的imageCaptionUserService提供标注功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/imageGallery/labeling")
@Validated
public class LabelingController {

    @Autowired
    private LabelingService labelingService;

    @Autowired
    private TrainingSampleService trainingSampleService;

    /**
     * 获取待标注样本
     *
     * @param request 获取样本请求
     * @return 标注样本数据
     */
    @PostMapping("/getNextLabelingSample")
    public Result<LabelingSampleSummary> getNextLabelingSample(@Valid @RequestBody LabelingSampleRequest request) {

        LabelingSampleSummary result = labelingService.getLabelingSample(request);

        return Result.success(result);
    }

    /**
     * 保存标注结果
     * 底层调用imageCaptionUserService的保存功能
     *
     * @param request 保存标注请求
     * @return 操作结果
     */
    @PostMapping("/saveLabels")
    public Result<?> saveLabels(@Valid @RequestBody SaveLabelsRequest request) {
        labelingService.saveLabels(request);
        return Result.success();
    }

    /**
     * 导出标注数据
     *
     * @param request 导出请求
     * @return 导出结果
     */
    @PostMapping("/exportLabeledData")
    public Result<ExportResultVO> exportLabeledData(@Valid @RequestBody ExportRequest request) {
        ExportResultVO result = labelingService.exportLabeledData(request.getRoundId());
        return Result.success(result);
    }

    /**
     * 获取标注进度
     */
    @PostMapping("/getProgress")
    public Result<LabelingProgressVO> getProgress(@NotNull @JsonArg Integer roundId) {
        LabelingProgressVO result = trainingSampleService.getLabelingProgress(roundId);

        return Result.success(result);
    }
}