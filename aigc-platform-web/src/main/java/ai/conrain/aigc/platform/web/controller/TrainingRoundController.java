package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * TrainingRound控制器
 *
 * <AUTHOR>
 * @version TrainingRoundService.java
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/trainingRound")
public class TrainingRoundController {

	/** trainingRoundService */
	@Autowired
	private TrainingRoundService trainingRoundService;
	
	@GetMapping("/getById/{id}")
	public Result<TrainingRoundVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(trainingRoundService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TrainingRoundVO trainingRound){
		return Result.success(trainingRoundService.insert(trainingRound).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		trainingRoundService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody TrainingRoundVO trainingRound){
		trainingRoundService.updateByIdSelective(trainingRound);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<TrainingRoundVO>> queryTrainingRoundList(@Valid @RequestBody TrainingRoundQuery query){
		return Result.success(trainingRoundService.queryTrainingRoundList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<TrainingRoundVO>> getTrainingRoundByPage(@Valid @RequestBody TrainingRoundQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(trainingRoundService.queryTrainingRoundByPage(query));
	}

    //挑选样本
	@PostMapping("/startSelectSamples")
	public Result startSelectSamples(@JsonArg @NotNull Integer roundId) {
        trainingRoundService.startSelectSamples(roundId);
        return Result.success();
	}

    @PostMapping("/querySelectedSampleResult")
    public Result<?> querySelectedSampleResult(@JsonArg @NotNull Integer roundId) throws IOException {
        trainingRoundService.querySelectedSampleResult(roundId);
        return Result.success();
    }

    // 模型性能验证
    @PostMapping("/startVerifyModelPerformance")
    public Result<?> verifyModelPerformance(@JsonArg @NotNull Integer roundId) {
        trainingRoundService.startVerifyModelPerformance(roundId);
        return Result.success();
    }

    @PostMapping("/queryModelPerformanceResult")
    public Result<?> queryModelPerformanceResult(@JsonArg @NotNull Integer roundId) throws IOException {
        trainingRoundService.queryModelPerformanceResult(roundId);
        return Result.success();
    }

    // 启动模型训练
    @PostMapping("/startTrainJob")
    public Result<?> startTrainJob(@JsonArg @NotNull Integer roundId) {
        trainingRoundService.startTrainJob(roundId);
        return Result.success();
    }

    @PostMapping("/queryTrainModelResult")
    public Result<?> queryTrainModelResult(@JsonArg @NotNull Integer roundId) throws IOException {
        trainingRoundService.queryTrainJobResult(roundId);
        return Result.success();
    }
}