package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.CaptionAttributeService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * CaptionAttribute控制器
 *
 * <AUTHOR>
 * @version CaptionAttributeService.java v 0.1 2025-08-14 07:49:02
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/captionAttribute")
public class CaptionAttributeController {

	/** captionAttributeService */
	@Autowired
	private CaptionAttributeService captionAttributeService;
	
	@GetMapping("/getById/{id}")
	public Result<CaptionAttributeVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(captionAttributeService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody CaptionAttributeVO captionAttribute){
		return Result.success(captionAttributeService.insert(captionAttribute).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		captionAttributeService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody CaptionAttributeVO captionAttribute){
		captionAttributeService.updateByIdSelective(captionAttribute);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<CaptionAttributeVO>> queryCaptionAttributeList(@Valid @RequestBody CaptionAttributeQuery query){
		return Result.success(captionAttributeService.queryCaptionAttributeList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<CaptionAttributeVO>> getCaptionAttributeByPage(@Valid @RequestBody CaptionAttributeQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(captionAttributeService.queryCaptionAttributeByPage(query));
	}
}