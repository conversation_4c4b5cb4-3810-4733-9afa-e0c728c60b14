package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.ai.model.ModelValidationResponse;
import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationResponse;
import ai.conrain.aigc.platform.integration.ai.model.TrainModelResponse;
import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import ai.conrain.aigc.platform.service.model.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class TrainingJobNotifyController {

    @Autowired
    private TrainingRoundService trainingRoundService;

    @PostMapping("/notify/onSelectedLabelSamples")
    public Result onSelectedLabelSamples(@RequestBody SamplePreparationResponse notify) {
        log.info("onSelectedLabelSamples:{}", notify);
        trainingRoundService.onLabelSamplesFinished(notify);
        return Result.success();
    }

    @PostMapping("/notify/onModelPerformanceVerified")
    public Result onModelPerformanceVerified(@RequestBody ModelValidationResponse notify) {
        log.info("onModelPerformanceVerified:{}", notify);
        trainingRoundService.onModelPerformanceVerified(notify);
        return Result.success();
    }

    @PostMapping("/notify/onTrainJobFinished")
    public Result onTrainJobFinished(@RequestBody TrainModelResponse notify) {
        log.info("onTrainJobFinished:{}", notify);
        trainingRoundService.onTrainJobFinished(notify);
        return Result.success();
    }
}
