package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;
import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.service.component.TrainingSampleService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * TrainingSample控制器
 *
 * <AUTHOR>
 * @version TrainingSampleService.java
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/trainingSample")
public class TrainingSampleController {

	/** trainingSampleService */
	@Autowired
	private TrainingSampleService trainingSampleService;
	
	@GetMapping("/getById/{id}")
	public Result<TrainingSampleVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(trainingSampleService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TrainingSampleVO trainingSample){
		return Result.success(trainingSampleService.insert(trainingSample).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		trainingSampleService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody TrainingSampleVO trainingSample){
		trainingSampleService.updateByIdSelective(trainingSample);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<TrainingSampleVO>> queryTrainingSampleList(@Valid @RequestBody TrainingSampleQuery query){
		return Result.success(trainingSampleService.queryTrainingSampleList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<TrainingSampleVO>> getTrainingSampleByPage(@Valid @RequestBody TrainingSampleQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(trainingSampleService.queryTrainingSampleByPage(query));
	}
}