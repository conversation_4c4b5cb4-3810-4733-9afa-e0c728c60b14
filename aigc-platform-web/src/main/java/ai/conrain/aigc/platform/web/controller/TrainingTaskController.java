package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.TrainingTaskService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.TrainingTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.TaskProgressVO;
import ai.conrain.aigc.platform.service.model.vo.TrainingTaskVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * TrainingTask控制器
 *
 * <AUTHOR>
 * @version TrainingTaskService.java
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/trainingTask")
public class TrainingTaskController {

	/** trainingTaskService */
	@Autowired
	private TrainingTaskService trainingTaskService;
	
	@GetMapping("/getById/{id}")
	public Result<TrainingTaskVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(trainingTaskService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TrainingTaskVO trainingTask){
		return Result.success(trainingTaskService.insert(trainingTask).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		trainingTaskService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody TrainingTaskVO trainingTask){
		trainingTaskService.updateByIdSelective(trainingTask);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<TrainingTaskVO>> queryTrainingTaskList(@Valid @RequestBody TrainingTaskQuery query){
		return Result.success(trainingTaskService.queryTrainingTaskList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<TrainingTaskVO>> getTrainingTaskByPage(@Valid @RequestBody TrainingTaskQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(trainingTaskService.queryTrainingTaskByPage(query));
	}

	/**
	 * 启动训练任务
	 */
	@PostMapping("/startTask")
	public Result<?> startTask(@NotNull @JsonArg Integer taskId) {
		try {
			TrainingTaskVO task = trainingTaskService.selectById(taskId);
			if (task == null) {
				return Result.failedWithMessage(ResultCode.PARAM_INVALID, "训练任务不存在");
			}
			
			if (!"pending".equals(task.getStatus()) && !"stopped".equals(task.getStatus())) {
				return Result.failedWithMessage(ResultCode.SYS_ERROR, "任务状态不允许启动");
			}
			
			// 启动任务逻辑
			trainingTaskService.startTask(taskId);
			return Result.success();
			
		} catch (Exception e) {
			log.error("启动训练任务失败, taskId: {}", taskId, e);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "启动任务失败: " + e.getMessage());
		}
	}

	/**
	 * 停止训练任务
	 */
	@PostMapping("/stopTask")
	public Result<?> stopTask(@NotNull @JsonArg Integer taskId) {
		try {
			TrainingTaskVO task = trainingTaskService.selectById(taskId);
			if (task == null) {
				return Result.failedWithMessage(ResultCode.PARAM_INVALID, "训练任务不存在");
			}
			
			if (!"running".equals(task.getStatus())) {
				return Result.failedWithMessage(ResultCode.SYS_ERROR, "任务状态不允许停止");
			}
			
			// 停止任务逻辑
			trainingTaskService.stopTask(taskId);
			return Result.success();
			
		} catch (Exception e) {
			log.error("停止训练任务失败, taskId: {}", taskId, e);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "停止任务失败: " + e.getMessage());
		}
	}

	/**
	 * 获取任务进度
	 */
	@PostMapping("/getTaskProgress")
	public Result<TaskProgressVO> getTaskProgress(@NotNull @JsonArg Integer taskId) {
		try {
			TaskProgressVO progress = trainingTaskService.getTaskProgress(taskId);
			return Result.success(progress);
		} catch (Exception e) {
			log.error("获取任务进度失败, taskId: {}", taskId, e);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "获取任务进度失败: " + e.getMessage());
		}
	}
}