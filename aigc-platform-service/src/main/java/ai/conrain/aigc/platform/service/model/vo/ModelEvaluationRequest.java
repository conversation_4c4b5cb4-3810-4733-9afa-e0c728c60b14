package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * ModelEvaluationRequest
 *
 * @version ModelEvaluationRequest.java
 */
@Data
public class ModelEvaluationRequest implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 模型路径 */
	private String modelPath;

	/** 测试数据路径 */
	private String testDataPath;

	/** 任务类型 binary/multiclass */
	private String taskType;

	/** 类别名称 */
	private List<String> classNames;
}