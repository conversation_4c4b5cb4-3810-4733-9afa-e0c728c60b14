package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

/**
 * 训练任务状态枚举
 */
@Getter
public enum TrainingTaskStatusEnum {
    /**
     * 等待中
     */
    pending("pending", "等待中"),

    /**
     * 运行中
     */
    running("running", "运行中"),

    /**
     * 已完成
     */
    completed("completed", "已完成"),

    /**
     * 已失败
     */
    failed("failed", "已失败"),

    /**
     * 已停止
     */
    stopped("stopped", "已停止");

    private final String code;
    private final String desc;

    TrainingTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TrainingTaskStatusEnum getByCode(String code) {
        for (TrainingTaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}