package ai.conrain.aigc.platform.service.model.vo.training;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TrainingSamplesModel {

    @JsonProperty("records")
    @JSONField(name = "records")
    private List<SampleRecordItem> records;

    @Data
    public static class SampleRecordItem {
        @JsonProperty("sample_id")
        @JSONField(name = "sample_id")
        private Integer sampleId;

        @JsonProperty("image_path")
        @JSONField(name = "image_path")
        private String imagePath;

        @JsonProperty("image_hash")
        @JSONField(name = "image_hash")
        private String imageHash;

        @JsonProperty("label")
        @JSONField(name = "label")
        private List<String> label;
    }
}
