package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 获取标注样本请求
 *
 * <AUTHOR>
 */
@Data
public class LabelingSampleRequest {
    
    /**
     * 训练任务轮次ID
     */
    @NotNull(message = "roundId不能为空")
    private Integer roundId;

    private Integer currentSampleId;

    //true取下一个，false取上一个
    private Boolean next = true;
}