package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 导出标注数据请求
 *
 * <AUTHOR>
 */
@Data
public class ExportRequest {
    
    /**
     * 训练任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
    
    /**
     * 训练轮次ID
     */
    @NotNull(message = "轮次ID不能为空")
    private Integer roundId;
    
    /**
     * 导出格式（可选）
     * json: JSON格式
     * csv: CSV格式
     * excel: Excel格式
     */
    private String format = "json";
    
    /**
     * 是否包含跳过的样本
     */
    private Boolean includeSkipped = false;
    
    /**
     * 是否包含未标注的样本
     */
    private Boolean includePending = false;
}