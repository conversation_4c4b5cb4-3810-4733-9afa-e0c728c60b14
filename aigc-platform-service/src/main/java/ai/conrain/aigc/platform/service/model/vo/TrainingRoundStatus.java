package ai.conrain.aigc.platform.service.model.vo;

import lombok.Getter;

/**
 * 训练轮次状态枚举
 */
@Getter
public enum TrainingRoundStatus {
    /**
     * 等待中
     */
    pending("pending"),

    /**
     * 训练中
     */
    training("training"),

    /**
     * 采样中
     */
    sampling("sampling"),

    /**
     * 打标中
     */
    labeling("labeling"),

    /**
     * 评估中
     */
    evaluating("evaluating"),

    /**
     * 评估审核中（人工决策是否通过）
     */
    evaluate_review("evaluate_review"),

    /**
     * 已完成
     */
    completed("completed"),

    /**
     * 已停止
     */
    stopped("stopped");

    private final String code;

    TrainingRoundStatus(String code) {
        this.code = code;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，找不到则返回null
     */
    public static TrainingRoundStatus getByCode(String code) {
        for (TrainingRoundStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}