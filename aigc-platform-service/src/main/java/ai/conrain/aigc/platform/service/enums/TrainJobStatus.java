package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

//训练作业状态：pending/running/success/failed
@Getter
public enum TrainJobStatus {
    pending("pending"),
    running("running"),
    success("success"),
    failed("failed");
    private final String code;
    TrainJobStatus(String code) {
        this.code = code;
    }
    public static TrainJobStatus getByCode(String code) {
        for (TrainJobStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
