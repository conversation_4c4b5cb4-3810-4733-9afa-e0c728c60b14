package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;

/**
 * SampleSelectionRequest
 *
 * @version SampleSelectionRequest.java
 */
@Data
public class SampleSelectionRequest implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 任务ID */
	private Integer taskId;

	/** 轮次ID */
	private Integer roundId;

	/** 策略 uncertainty/committee/random */
	private String strategy;

	/** 样本数量 */
	private Integer sampleCount;

	/** 当前模型路径 */
	private String modelPath;

	/** 样本池路径 */
	private String dataPoolPath;

	/** 委员会投票比例 */
	private String votingRatio;
}