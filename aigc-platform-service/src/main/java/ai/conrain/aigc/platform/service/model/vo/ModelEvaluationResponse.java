package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * ModelEvaluationResponse
 *
 * @version ModelEvaluationResponse.java
 */
@Data
public class ModelEvaluationResponse implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 准确率 */
	private Double accuracy;

	/** F1分数 */
	private Double f1Score;

	/** 精确率 */
	private Double precision;

	/** 召回率 */
	private Double recall;

	/** 各类别指标 */
	private Map<String, Double> classMetrics;

	/** 混淆矩阵 JSON格式 */
	private String confusionMatrix;
}