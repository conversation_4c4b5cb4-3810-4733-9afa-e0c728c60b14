package ai.conrain.aigc.platform.service.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用 CSV 字符串解析示例
 * 不依赖预定义的列名，可以处理任意格式的 CSV 数据
 */
@Slf4j
public class CsvParseUtil {

    public static List<Map<String, String>> parseCsvWithHeaders(String csvData){
        return parseCsvWithHeaders(csvData, null, false);
    }

    /**
     * 解析带有标题行的 CSV 字符串
     * 
     * @param csvData CSV 字符串数据
     * @return 解析结果，包含所有行数据的列表
     */
    public static List<Map<String, String>> parseCsvWithHeaders(String csvData, List<String> columnFilters, boolean fromFile) {
        // 使用默认格式，将第一行作为标题
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader()
                .setSkipHeaderRecord(true)
                .build();

        try {
            List<Map<String, String>> result = new ArrayList<>();
            try (Reader reader = fromFile ? new FileReader(csvData) : new StringReader(csvData);
                 CSVParser csvParser = new CSVParser(reader, csvFormat)) {

                // 获取标题名称
                List<String> headers = csvParser.getHeaderNames();

                // 遍历所有记录
                for (CSVRecord record : csvParser) {
                    Map<String, String> rowMap = new HashMap<>();
                    // 通过列名访问数据
                    for (int i = 0; i < headers.size(); i++) {
                        String columnName = headers.get(i);
                        if (CollectionUtils.isNotEmpty(columnFilters) && !columnFilters.contains(columnName)){
                            continue;
                        }
                        rowMap.put(columnName, record.get(i));
                    }
                    result.add(rowMap);
                }
            }

            return result;
        } catch (IOException e) {
            log.error("Error parsing CSV data: {}", csvData);
            throw new RuntimeException("Error parsing CSV data", e);
        }

    }
}