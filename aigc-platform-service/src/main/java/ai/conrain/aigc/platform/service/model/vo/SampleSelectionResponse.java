package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * SampleSelectionResponse
 *
 * @version SampleSelectionResponse.java
 */
@Data
public class SampleSelectionResponse implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 选中的样本路径列表 */
	private List<String> selectedSamples;

	/** 生成的CSV文件路径 */
	private String csvPath;

	/** 总数量 */
	private Integer totalCount;

	/** 策略 */
	private String strategy;
}