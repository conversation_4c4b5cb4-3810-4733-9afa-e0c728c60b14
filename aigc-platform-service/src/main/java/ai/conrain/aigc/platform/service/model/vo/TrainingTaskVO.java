package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TrainingTaskStatusEnum;
import ai.conrain.aigc.platform.service.model.vo.training.TrainingConfig;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * TrainingTaskVO
 *
 * @version TrainingTaskService.java
 */
@Data
public class TrainingTaskVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键，自增 */
	private Integer id;

	/** 任务名称 */
	private String name;

	/** 任务详细描述 */
	private String description;

	/** 模型应用场景，如低质量分类、单图好中差分类 等 */
	private ModelSceneEnum modelScene;

	/** 训练核心配置 */
	private TrainingConfig trainingConfig;

	/** 任务整体状态 */
	private TrainingTaskStatusEnum status;

	/** 当前正在执行的轮次序号（从 1 开始） */
	private Integer currentRoundNum;

	/** 扩展字段，JSONB 灵活存储 */
	private String extInfo;

	/** 记录创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 记录最后修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}