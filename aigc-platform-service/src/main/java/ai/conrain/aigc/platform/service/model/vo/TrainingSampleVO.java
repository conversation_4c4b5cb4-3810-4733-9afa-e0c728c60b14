package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * TrainingSampleVO
 *
 * @version TrainingSampleService.java
 */
@Data
public class TrainingSampleVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键，自增 */
	private Integer id;

	/** 样本数据ID */
	private Integer sampleDataId;

	/** 样本数据ID类型 */
	private String sampleDataIdType;

	/** 样本标注ID */
	private Integer sampleCaptionId;

	/** 样本标注ID类型 */
	private String sampleCaptionIdType;

	/** 关联训练ID */
	private Integer relatedTrainingId;

	/** 关联训练ID类型 */
	private String relatedTrainingIdType;

	/** 扩展信息字段 */
	private String extInfo;

	/** 记录创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 记录最后修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

    //view fields
    private ImageVO imageVO;
    private com.alibaba.fastjson2.JSONObject caption;
}