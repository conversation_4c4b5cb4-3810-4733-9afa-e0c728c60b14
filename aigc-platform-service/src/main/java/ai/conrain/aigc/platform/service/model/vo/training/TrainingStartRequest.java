package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;

/**
 * 训练启动请求
 */
@Data
public class TrainingStartRequest {
    private Integer taskId;
    private Integer roundId;
    private String trainingWay; // pretrain/incremental/committee
    private String modelScene;
    private String trainingConfig; // JSON配置
    private String datasetPath; // 训练数据路径
    private String modelOutputPath; // 模型输出路径
    private String baseModelPath; // 基础模型路径（增量训练时使用）
    private Integer maxEpochs; // 最大训练轮数
    private Double learningRate; // 学习率
    private Integer batchSize; // 批次大小
}