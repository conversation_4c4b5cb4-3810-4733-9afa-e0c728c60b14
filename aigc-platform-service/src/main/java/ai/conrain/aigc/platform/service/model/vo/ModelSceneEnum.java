package ai.conrain.aigc.platform.service.model.vo;

import lombok.Getter;

/**
 * 模型应用场景枚举
 */
@Getter
public enum ModelSceneEnum {
    /**
     * 低质量分类
     */
    low_quality("low_quality", "低质量分类"),

    //流派分类
    genre_classification("genre_classification", "流派分类"),

    /**
     * 单图好中差分类
     */
    single_image_quality("single_image_quality", "单图好中差分类");

    private final String code;
    private final String desc;

    ModelSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static ModelSceneEnum getByCode(String code) {
        for (ModelSceneEnum value : ModelSceneEnum.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}