package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * TaskProgressVO
 *
 * @version TaskProgressVO.java
 */
@Data
public class TaskProgressVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 任务ID */
	private Integer taskId;

	/** 任务状态 */
	private String status;

	/** 当前轮次 */
	private Integer currentRound;

	/** 总样本数 */
	private Integer totalSamples;

	/** 已标注样本数 */
	private Integer labeledSamples;

	/** 进度百分比 (0.0-1.0) */
	private Double progress;

	/** 轮次列表 */
	private List<TrainingRoundVO> rounds;
}