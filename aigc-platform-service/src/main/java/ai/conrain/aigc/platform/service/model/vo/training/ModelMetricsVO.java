package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.List;

/**
 * 模型指标VO
 */
@Data
public class ModelMetricsVO {
    private Double accuracy;
    private Double f1Score;
    private Double precision;
    private Double recall;
    private Double loss;
    private List<ClassMetricVO> classMetrics;
}

/**
 * 类别指标VO
 */
@Data
class ClassMetricVO {
    private String className;
    private Double precision;
    private Double recall;
    private Double f1Score;
    private Integer support; // 样本数量
}