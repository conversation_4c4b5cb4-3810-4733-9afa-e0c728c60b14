package ai.conrain.aigc.platform.service.model.vo.training;

import ai.conrain.aigc.platform.service.enums.TrainingWayEnum;
import ai.conrain.aigc.platform.service.model.vo.ModelSceneEnum;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 任务配置VO
 */
@Data
public class TrainingConfig {
    // 已选择的属性，动态打标
    private List<Integer> selectedAttributes;
    // 基础模型路径，用于挑选样本
    private String baseModelPathOrDir;

    private Boolean removeMultiPerson = true;
    private Boolean removePersonWithHeightRatio = true;

    /**
     * 训练方式
     * @see TrainingWayEnum
     */
    private String trainingWay;

    /** 本任务期望采集/使用的总样本量 */
    private Integer samplesNum;

    private Integer epochs = 10;

    private Integer batchSize = 64;

    private Double learningRate = 1e-5;
}