package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.example.TrainingRoundExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.TrainingRoundDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundStatus;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class PollingTrainingStatus extends JavaProcessor {

    @Autowired
    private TrainingRoundService trainingRoundService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            TrainingRoundQuery query = new TrainingRoundQuery();
            query.setStatus(TrainingRoundStatus.sampling.getCode());
            List<TrainingRoundVO> trainingRoundVOS = trainingRoundService.queryTrainingRoundList(query);
            for (TrainingRoundVO trainingRoundVO : trainingRoundVOS) {
                MDC.put("traceId", uuid + "_" + trainingRoundVO.getId());
                trainingRoundService.querySelectedSampleResult(trainingRoundVO.getId());
            }

            query.setStatus(TrainingRoundStatus.evaluating.getCode());
            trainingRoundVOS = trainingRoundService.queryTrainingRoundList(query);
            for (TrainingRoundVO trainingRoundVO : trainingRoundVOS) {
                MDC.put("traceId", uuid + "_" + trainingRoundVO.getId());
                trainingRoundService.queryModelPerformanceResult(trainingRoundVO.getId());
            }

            query.setStatus(TrainingRoundStatus.training.getCode());
            trainingRoundVOS = trainingRoundService.queryTrainingRoundList(query);
            for (TrainingRoundVO trainingRoundVO : trainingRoundVOS) {
                MDC.put("traceId", uuid + "_" + trainingRoundVO.getId());
                trainingRoundService.queryTrainJobResult(trainingRoundVO.getId());
            }

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
