package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;

/**
 * TrainingStartRequest
 *
 * @version TrainingStartRequest.java
 */
@Data
public class TrainingStartRequest implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 任务ID */
	private Integer taskId;

	/** 轮次ID */
	private Integer roundId;

	/** 训练方式 pretrain/incremental/committee */
	private String trainingWay;

	/** 模型场景 */
	private String modelScene;

	/** 训练配置 JSON配置 */
	private String trainingConfig;

	/** 训练数据路径 */
	private String datasetPath;

	/** 模型输出路径 */
	private String modelOutputPath;
}