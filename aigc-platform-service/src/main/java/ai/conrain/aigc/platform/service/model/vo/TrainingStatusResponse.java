package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * TrainingStatusResponse
 *
 * @version TrainingStatusResponse.java
 */
@Data
public class TrainingStatusResponse implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 状态 running/completed/failed */
	private String status;

	/** 进度 0.0-1.0 */
	private Double progress;

	/** 消息 */
	private String message;

	/** 训练指标 */
	private ModelMetricsVO metrics;

	/** 开始时间 */
	private Date startTime;

	/** 结束时间 */
	private Date endTime;
}