package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.List;

/**
 * 模型评估请求
 */
@Data
public class ModelEvaluationRequest {
    private String modelPath;
    private String testDataPath;
    private String taskType; // binary/multiclass
    private List<String> classNames;
    private String evaluationMetrics; // 评估指标配置
    private Boolean generateConfusionMatrix; // 是否生成混淆矩阵
    private Boolean generateClassificationReport; // 是否生成分类报告
}