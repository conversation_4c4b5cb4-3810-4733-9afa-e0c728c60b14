package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TrainJobStatus;
import ai.conrain.aigc.platform.service.model.vo.training.TrainingConfig;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * TrainingRoundVO
 *
 * @version TrainingRoundService.java
 */
@Data
public class TrainingRoundVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键，自增 */
	private Integer id;

	/** 关联的训练任务ID */
	private Integer taskId;

    /** 模型标识 */
    private String modelScene;

	/** 轮次编号，从0开始 */
	private Integer roundNumber;

	/** 轮次状态：pending/training/sampling/labeling/evaluating/completed/stopped */
	private TrainingRoundStatus status;

	/** 本轮训练配置 */
	private TrainingConfig trainingConfig;

	/** 训练样本导出信息 */
	private String trainingSampleExport;

    /**
     * 训练作业状态：pending/running/success/failed
     * @see TrainJobStatus
     */
	private String trainStatus;

	/** 训练作业开始时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date trainStartedTime;

	/** 训练作业完成时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date trainCompletedTime;

	/** 模型版本 */
	private String modelVersion;

	/** 模型文件存储路径 */
	private String modelFilePath;

	/** 模型访问URL */
	private String modelUrl;

	/** 模型评估指标，如准确率、F1值等 */
	private String modelPerformanceMetrics;

	/** 扩展信息字段 */
	private JSONObject extInfo;

	/** 记录创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 记录最后修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

    public JSONObject getExtInfo() {
        if (extInfo == null){
            extInfo = new JSONObject();
        }
        return extInfo;
    }
}