package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 模型预测响应
 */
@Data
public class ModelPredictionResponse {
    private List<PredictionResultVO> predictions;
    private String outputPath; // 输出文件路径
    private Integer totalSamples;
    private Map<String, Integer> predictionSummary; // 预测结果统计
}

/**
 * 单个预测结果
 */
@Data
class PredictionResultVO {
    private String samplePath;
    private String predictedClass;
    private Double confidence;
    private Map<String, Double> classProbabilities; // 各类别概率
}