package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

/**
 * 训练方式枚举
 */
@Getter
public enum TrainingWayEnum {

    /**
     * 增量训练
     */
    incremental("incremental", "增量训练"),

    /**
     * 委员会训练（只用分歧样本）
     */
    committee("committee", "委员会训练");

    private final String code;
    private final String desc;

    TrainingWayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TrainingWayEnum getByCode(String code) {
        for (TrainingWayEnum trainingWay : values()) {
            if (trainingWay.getCode().equals(code)) {
                return trainingWay;
            }
        }
        return null;
    }
}