package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.Date;
import java.util.Map;

/**
 * 训练状态响应
 */
@Data
public class TrainingStatusResponse {
    private String status; // running/completed/failed/stopped
    private Double progress; // 0.0-1.0
    private String message;
    private ModelMetricsVO metrics; // 训练指标
    private Date startTime;
    private Date endTime;
    private Integer currentEpoch;
    private Integer totalEpochs;
    private Double currentLoss;
    private Double currentAccuracy;
    private String errorMessage; // 错误信息（如果失败）
    private Map<String, Object> additionalInfo; // 额外信息
}