package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 验证标注数据请求
 *
 * <AUTHOR>
 */
@Data
public class ValidateLabelsRequest {
    
    /**
     * 训练任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
    
    /**
     * 训练轮次ID
     */
    @NotNull(message = "轮次ID不能为空")
    private Integer roundId;
    
    /**
     * 验证规则（可选）
     * strict: 严格验证，所有属性都必须有值
     * loose: 宽松验证，允许部分属性为空
     */
    private String validationMode = "strict";
}