package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 模型评估响应
 */
@Data
public class ModelEvaluationResponse {
    private Double accuracy;
    private Double f1Score;
    private Double precision;
    private Double recall;
    private Map<String, Double> classMetrics; // 各类别指标
    private String confusionMatrix; // JSON格式混淆矩阵
    private List<ClassMetricVO> detailedClassMetrics; // 详细类别指标
    private String evaluationReport; // 评估报告
    private Map<String, Object> additionalMetrics; // 额外指标
}