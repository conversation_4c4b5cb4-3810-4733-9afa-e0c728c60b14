package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.model.common.FilePathInfo;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件路径解析工具类
 * 用于拆分文件路径为目录、文件名和扩展名
 */
public class FilePathParseUtil {

    /**
     * 解析文件路径，拆分为目录、文件名和扩展名
     *
     * @param filePath 文件路径
     * @return 包含目录、文件名和扩展名的FilePathInfo对象
     */
    public static FilePathInfo parseFilePath(String filePath) {
        // 使用 Paths.get() 创建 Path 对象
        Path path = Paths.get(filePath);

        // 获取文件所在的目录
        Path parent = path.getParent();
        String directory = parent != null ? parent.toString() : "";

        // 获取文件名（包含扩展名）
        String fileNameWithExtension = path.getFileName().toString();

        // 分离文件名和扩展名
        String fileName;
        String extension;

        int lastDotIndex = fileNameWithExtension.lastIndexOf('.');
        if (lastDotIndex > 0) { // 确保点号存在且不在第一位
            fileName = fileNameWithExtension.substring(0, lastDotIndex);
            extension = fileNameWithExtension.substring(lastDotIndex + 1);
        } else {
            fileName = fileNameWithExtension;
            extension = "";
        }

        return new FilePathInfo(directory, fileNameWithExtension, fileName, extension);
    }

    /**
     * 获取文件路径中的目录部分
     *
     * @param filePath 文件路径
     * @return 目录部分
     */
    public static String getDirectory(String filePath) {
        Path path = Paths.get(filePath);
        Path parent = path.getParent();
        return parent != null ? parent.toString() : "";
    }

    /**
     * 获取文件名（包含扩展名）
     *
     * @param filePath 文件路径
     * @return 文件名（包含扩展名）
     */
    public static String getFileNameWithExtension(String filePath) {
        Path path = Paths.get(filePath);
        return path.getFileName().toString();
    }

    /**
     * 获取文件名（不包含扩展名）
     *
     * @param filePath 文件路径
     * @return 文件名（不包含扩展名）
     */
    public static String getFileNameWithoutExtension(String filePath) {
        String fileNameWithExtension = getFileNameWithExtension(filePath);
        int lastDotIndex = fileNameWithExtension.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileNameWithExtension.substring(0, lastDotIndex);
        }
        return fileNameWithExtension;
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 文件扩展名
     */
    public static String getFileExtensionNoDot(String filePath) {
        String fileNameWithExtension = getFileNameWithExtension(filePath);
        int lastDotIndex = fileNameWithExtension.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileNameWithExtension.length() - 1) {
            return fileNameWithExtension.substring(lastDotIndex + 1);
        }
        return "";
    }
}