package ai.conrain.aigc.platform.service.model.vo.training;

import lombok.Data;
import java.util.List;

/**
 * 模型架构响应
 */
@Data
public class ModelArchitectureResponse {
    private List<ModelArchitectureVO> architectures;
}

/**
 * 模型架构信息
 */
@Data
class ModelArchitectureVO {
    private String name;
    private String displayName;
    private String description;
    private List<String> supportedTaskTypes; // 支持的任务类型
    private List<String> supportedFeatureTypes; // 支持的特征类型
    private Boolean isDefault; // 是否为默认架构
}