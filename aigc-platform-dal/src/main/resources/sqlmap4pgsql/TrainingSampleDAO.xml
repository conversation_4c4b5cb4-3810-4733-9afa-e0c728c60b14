<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.TrainingSampleDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sample_data_id" jdbcType="INTEGER" property="sampleDataId" />
    <result column="sample_data_id_type" jdbcType="VARCHAR" property="sampleDataIdType" />
    <result column="sample_caption_id" jdbcType="INTEGER" property="sampleCaptionId" />
    <result column="sample_caption_id_type" jdbcType="VARCHAR" property="sampleCaptionIdType" />
    <result column="related_training_id" jdbcType="INTEGER" property="relatedTrainingId" />
    <result column="related_training_id_type" jdbcType="VARCHAR" property="relatedTrainingIdType" />
    <result column="ext_info" jdbcType="OTHER" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sample_data_id, sample_data_id_type, sample_caption_id, sample_caption_id_type, 
    related_training_id, related_training_id_type, ext_info, create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.TrainingSampleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from training_sample
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from training_sample
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from training_sample
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO" useGeneratedKeys="true">
    insert into training_sample (sample_data_id, sample_data_id_type, sample_caption_id, 
      sample_caption_id_type, related_training_id, 
      related_training_id_type, ext_info, create_time, 
      modify_time)
    values (#{sampleDataId,jdbcType=INTEGER}, #{sampleDataIdType,jdbcType=VARCHAR}, #{sampleCaptionId,jdbcType=INTEGER}, 
      #{sampleCaptionIdType,jdbcType=VARCHAR}, #{relatedTrainingId,jdbcType=INTEGER}, 
      #{relatedTrainingIdType,jdbcType=VARCHAR}, #{extInfo,jdbcType=OTHER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO" useGeneratedKeys="true">
    insert into training_sample
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sampleDataId != null">
        sample_data_id,
      </if>
      <if test="sampleDataIdType != null">
        sample_data_id_type,
      </if>
      <if test="sampleCaptionId != null">
        sample_caption_id,
      </if>
      <if test="sampleCaptionIdType != null">
        sample_caption_id_type,
      </if>
      <if test="relatedTrainingId != null">
        related_training_id,
      </if>
      <if test="relatedTrainingIdType != null">
        related_training_id_type,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sampleDataId != null">
        #{sampleDataId,jdbcType=INTEGER},
      </if>
      <if test="sampleDataIdType != null">
        #{sampleDataIdType,jdbcType=VARCHAR},
      </if>
      <if test="sampleCaptionId != null">
        #{sampleCaptionId,jdbcType=INTEGER},
      </if>
      <if test="sampleCaptionIdType != null">
        #{sampleCaptionIdType,jdbcType=VARCHAR},
      </if>
      <if test="relatedTrainingId != null">
        #{relatedTrainingId,jdbcType=INTEGER},
      </if>
      <if test="relatedTrainingIdType != null">
        #{relatedTrainingIdType,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.TrainingSampleExample" resultType="java.lang.Long">
    select count(*) from training_sample
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update training_sample
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sampleDataId != null">
        sample_data_id = #{record.sampleDataId,jdbcType=INTEGER},
      </if>
      <if test="record.sampleDataIdType != null">
        sample_data_id_type = #{record.sampleDataIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleCaptionId != null">
        sample_caption_id = #{record.sampleCaptionId,jdbcType=INTEGER},
      </if>
      <if test="record.sampleCaptionIdType != null">
        sample_caption_id_type = #{record.sampleCaptionIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedTrainingId != null">
        related_training_id = #{record.relatedTrainingId,jdbcType=INTEGER},
      </if>
      <if test="record.relatedTrainingIdType != null">
        related_training_id_type = #{record.relatedTrainingIdType,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=OTHER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update training_sample
    set id = #{record.id,jdbcType=INTEGER},
      sample_data_id = #{record.sampleDataId,jdbcType=INTEGER},
      sample_data_id_type = #{record.sampleDataIdType,jdbcType=VARCHAR},
      sample_caption_id = #{record.sampleCaptionId,jdbcType=INTEGER},
      sample_caption_id_type = #{record.sampleCaptionIdType,jdbcType=VARCHAR},
      related_training_id = #{record.relatedTrainingId,jdbcType=INTEGER},
      related_training_id_type = #{record.relatedTrainingIdType,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=OTHER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO">
    update training_sample
    <set>
      <if test="sampleDataId != null">
        sample_data_id = #{sampleDataId,jdbcType=INTEGER},
      </if>
      <if test="sampleDataIdType != null">
        sample_data_id_type = #{sampleDataIdType,jdbcType=VARCHAR},
      </if>
      <if test="sampleCaptionId != null">
        sample_caption_id = #{sampleCaptionId,jdbcType=INTEGER},
      </if>
      <if test="sampleCaptionIdType != null">
        sample_caption_id_type = #{sampleCaptionIdType,jdbcType=VARCHAR},
      </if>
      <if test="relatedTrainingId != null">
        related_training_id = #{relatedTrainingId,jdbcType=INTEGER},
      </if>
      <if test="relatedTrainingIdType != null">
        related_training_id_type = #{relatedTrainingIdType,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO">
    update training_sample
    set sample_data_id = #{sampleDataId,jdbcType=INTEGER},
      sample_data_id_type = #{sampleDataIdType,jdbcType=VARCHAR},
      sample_caption_id = #{sampleCaptionId,jdbcType=INTEGER},
      sample_caption_id_type = #{sampleCaptionIdType,jdbcType=VARCHAR},
      related_training_id = #{relatedTrainingId,jdbcType=INTEGER},
      related_training_id_type = #{relatedTrainingIdType,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=OTHER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>