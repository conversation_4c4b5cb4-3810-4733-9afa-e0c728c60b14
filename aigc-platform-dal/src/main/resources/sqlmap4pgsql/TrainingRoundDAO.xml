<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.TrainingRoundDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="round_number" jdbcType="INTEGER" property="roundNumber" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="training_config" jdbcType="OTHER" property="trainingConfig" />
    <result column="training_sample_export" jdbcType="VARCHAR" property="trainingSampleExport" />
    <result column="train_status" jdbcType="VARCHAR" property="trainStatus" />
    <result column="train_started_time" jdbcType="TIMESTAMP" property="trainStartedTime" />
    <result column="train_completed_time" jdbcType="TIMESTAMP" property="trainCompletedTime" />
    <result column="model_scene" jdbcType="VARCHAR" property="modelScene" />
    <result column="model_version" jdbcType="VARCHAR" property="modelVersion" />
    <result column="model_file_path" jdbcType="VARCHAR" property="modelFilePath" />
    <result column="model_url" jdbcType="VARCHAR" property="modelUrl" />
    <result column="model_performance_metrics" jdbcType="OTHER" property="modelPerformanceMetrics" />
    <result column="ext_info" jdbcType="OTHER" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, round_number, status, training_config, training_sample_export, train_status, 
    train_started_time, train_completed_time, model_scene, model_version, model_file_path, 
    model_url, model_performance_metrics, ext_info, create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.TrainingRoundExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from training_round
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from training_round
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from training_round
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO" useGeneratedKeys="true">
    insert into training_round (task_id, round_number, status, 
      training_config, training_sample_export, train_status, 
      train_started_time, train_completed_time, 
      model_scene, model_version, model_file_path, 
      model_url, model_performance_metrics, ext_info, 
      create_time, modify_time)
    values (#{taskId,jdbcType=INTEGER}, #{roundNumber,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{trainingConfig,jdbcType=OTHER}, #{trainingSampleExport,jdbcType=OTHER}, #{trainStatus,jdbcType=VARCHAR}, 
      #{trainStartedTime,jdbcType=TIMESTAMP}, #{trainCompletedTime,jdbcType=TIMESTAMP}, 
      #{modelScene,jdbcType=VARCHAR}, #{modelVersion,jdbcType=VARCHAR}, #{modelFilePath,jdbcType=VARCHAR}, 
      #{modelUrl,jdbcType=VARCHAR}, #{modelPerformanceMetrics,jdbcType=OTHER}, #{extInfo,jdbcType=OTHER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO" useGeneratedKeys="true">
    insert into training_round
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="roundNumber != null">
        round_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="trainingConfig != null">
        training_config,
      </if>
      <if test="trainingSampleExport != null">
        training_sample_export,
      </if>
      <if test="trainStatus != null">
        train_status,
      </if>
      <if test="trainStartedTime != null">
        train_started_time,
      </if>
      <if test="trainCompletedTime != null">
        train_completed_time,
      </if>
      <if test="modelScene != null">
        model_scene,
      </if>
      <if test="modelVersion != null">
        model_version,
      </if>
      <if test="modelFilePath != null">
        model_file_path,
      </if>
      <if test="modelUrl != null">
        model_url,
      </if>
      <if test="modelPerformanceMetrics != null">
        model_performance_metrics,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="roundNumber != null">
        #{roundNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="trainingConfig != null">
        #{trainingConfig,jdbcType=OTHER},
      </if>
      <if test="trainingSampleExport != null">
        #{trainingSampleExport,jdbcType=OTHER},
      </if>
      <if test="trainStatus != null">
        #{trainStatus,jdbcType=VARCHAR},
      </if>
      <if test="trainStartedTime != null">
        #{trainStartedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainCompletedTime != null">
        #{trainCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modelScene != null">
        #{modelScene,jdbcType=VARCHAR},
      </if>
      <if test="modelVersion != null">
        #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="modelFilePath != null">
        #{modelFilePath,jdbcType=VARCHAR},
      </if>
      <if test="modelUrl != null">
        #{modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="modelPerformanceMetrics != null">
        #{modelPerformanceMetrics,jdbcType=OTHER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.TrainingRoundExample" resultType="java.lang.Long">
    select count(*) from training_round
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

    <select id="lockByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from training_round
        where id = #{id,jdbcType=INTEGER}
        for update nowait
    </select>

    <update id="updateByExampleSelective" parameterType="map">
    update training_round
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=INTEGER},
      </if>
      <if test="record.roundNumber != null">
        round_number = #{record.roundNumber,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.trainingConfig != null">
        training_config = #{record.trainingConfig,jdbcType=OTHER},
      </if>
      <if test="record.trainingSampleExport != null">
        training_sample_export = #{record.trainingSampleExport,jdbcType=VARCHAR},
      </if>
      <if test="record.trainStatus != null">
        train_status = #{record.trainStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.trainStartedTime != null">
        train_started_time = #{record.trainStartedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.trainCompletedTime != null">
        train_completed_time = #{record.trainCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modelScene != null">
        model_scene = #{record.modelScene,jdbcType=VARCHAR},
      </if>
      <if test="record.modelVersion != null">
        model_version = #{record.modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.modelFilePath != null">
        model_file_path = #{record.modelFilePath,jdbcType=VARCHAR},
      </if>
      <if test="record.modelUrl != null">
        model_url = #{record.modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.modelPerformanceMetrics != null">
        model_performance_metrics = #{record.modelPerformanceMetrics,jdbcType=OTHER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=OTHER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update training_round
    set id = #{record.id,jdbcType=INTEGER},
      task_id = #{record.taskId,jdbcType=INTEGER},
      round_number = #{record.roundNumber,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      training_config = #{record.trainingConfig,jdbcType=OTHER},
      training_sample_export = #{record.trainingSampleExport,jdbcType=VARCHAR},
      train_status = #{record.trainStatus,jdbcType=VARCHAR},
      train_started_time = #{record.trainStartedTime,jdbcType=TIMESTAMP},
      train_completed_time = #{record.trainCompletedTime,jdbcType=TIMESTAMP},
      model_scene = #{record.modelScene,jdbcType=VARCHAR},
      model_version = #{record.modelVersion,jdbcType=VARCHAR},
      model_file_path = #{record.modelFilePath,jdbcType=VARCHAR},
      model_url = #{record.modelUrl,jdbcType=VARCHAR},
      model_performance_metrics = #{record.modelPerformanceMetrics,jdbcType=OTHER},
      ext_info = #{record.extInfo,jdbcType=OTHER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO">
    update training_round
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="roundNumber != null">
        round_number = #{roundNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="trainingConfig != null">
        training_config = #{trainingConfig,jdbcType=OTHER},
      </if>
      <if test="trainingSampleExport != null">
        training_sample_export = #{trainingSampleExport,jdbcType=VARCHAR},
      </if>
      <if test="trainStatus != null">
        train_status = #{trainStatus,jdbcType=VARCHAR},
      </if>
      <if test="trainStartedTime != null">
        train_started_time = #{trainStartedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainCompletedTime != null">
        train_completed_time = #{trainCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modelScene != null">
        model_scene = #{modelScene,jdbcType=VARCHAR},
      </if>
      <if test="modelVersion != null">
        model_version = #{modelVersion,jdbcType=VARCHAR},
      </if>
      <if test="modelFilePath != null">
        model_file_path = #{modelFilePath,jdbcType=VARCHAR},
      </if>
      <if test="modelUrl != null">
        model_url = #{modelUrl,jdbcType=VARCHAR},
      </if>
      <if test="modelPerformanceMetrics != null">
        model_performance_metrics = #{modelPerformanceMetrics,jdbcType=OTHER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO">
    update training_round
    set task_id = #{taskId,jdbcType=INTEGER},
      round_number = #{roundNumber,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      training_config = #{trainingConfig,jdbcType=OTHER},
      training_sample_export = #{trainingSampleExport,jdbcType=VARCHAR},
      train_status = #{trainStatus,jdbcType=VARCHAR},
      train_started_time = #{trainStartedTime,jdbcType=TIMESTAMP},
      train_completed_time = #{trainCompletedTime,jdbcType=TIMESTAMP},
      model_scene = #{modelScene,jdbcType=VARCHAR},
      model_version = #{modelVersion,jdbcType=VARCHAR},
      model_file_path = #{modelFilePath,jdbcType=VARCHAR},
      model_url = #{modelUrl,jdbcType=VARCHAR},
      model_performance_metrics = #{modelPerformanceMetrics,jdbcType=OTHER},
      ext_info = #{extInfo,jdbcType=OTHER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>