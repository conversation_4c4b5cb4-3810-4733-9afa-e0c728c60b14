package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 训练任务主表
 * 对应数据表：training_task
 */
@Data
public class TrainingTaskDO implements Serializable {
    /**
     * 主键，自增
     */
    private Integer id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务详细描述
     */
    private String description;

    /**
     * 模型应用场景，如低质量分类、单图好中差分类 等
     */
    private String modelScene;

    /**
     * 训练核心配置 JSONB：含分类任务类型（二分类/多分类）、特征提取类型、样本占比规则等
     */
    private String trainingConfig;

    /**
     * 任务整体状态：pending/running/completed/failed/stopped
     */
    private String status;

    /**
     * 当前正在执行的轮次序号（从 1 开始）
     */
    private Integer currentRoundNum;

    /**
     * 扩展字段，JSONB 灵活存储
     */
    private String extInfo;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录最后修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}