package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.TrainingTaskExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingTaskDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TrainingTaskDAO {
    long countByExample(TrainingTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TrainingTaskDO record);

    int insertSelective(TrainingTaskDO record);

    List<TrainingTaskDO> selectByExample(TrainingTaskExample example);

    TrainingTaskDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TrainingTaskDO record, @Param("example") TrainingTaskExample example);

    int updateByExample(@Param("record") TrainingTaskDO record, @Param("example") TrainingTaskExample example);

    int updateByPrimaryKeySelective(TrainingTaskDO record);

    int updateByPrimaryKey(TrainingTaskDO record);
}