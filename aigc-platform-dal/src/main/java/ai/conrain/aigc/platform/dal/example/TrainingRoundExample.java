package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrainingRoundExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public TrainingRoundExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Integer value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Integer value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Integer value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Integer value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Integer value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Integer> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Integer> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Integer value1, Integer value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Integer value1, Integer value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andRoundNumberIsNull() {
            addCriterion("round_number is null");
            return (Criteria) this;
        }

        public Criteria andRoundNumberIsNotNull() {
            addCriterion("round_number is not null");
            return (Criteria) this;
        }

        public Criteria andRoundNumberEqualTo(Integer value) {
            addCriterion("round_number =", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberNotEqualTo(Integer value) {
            addCriterion("round_number <>", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberGreaterThan(Integer value) {
            addCriterion("round_number >", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("round_number >=", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberLessThan(Integer value) {
            addCriterion("round_number <", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberLessThanOrEqualTo(Integer value) {
            addCriterion("round_number <=", value, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberIn(List<Integer> values) {
            addCriterion("round_number in", values, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberNotIn(List<Integer> values) {
            addCriterion("round_number not in", values, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberBetween(Integer value1, Integer value2) {
            addCriterion("round_number between", value1, value2, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andRoundNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("round_number not between", value1, value2, "roundNumber");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigIsNull() {
            addCriterion("training_config is null");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigIsNotNull() {
            addCriterion("training_config is not null");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigEqualTo(String value) {
            addCriterion("training_config =", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigNotEqualTo(String value) {
            addCriterion("training_config <>", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigGreaterThan(String value) {
            addCriterion("training_config >", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigGreaterThanOrEqualTo(String value) {
            addCriterion("training_config >=", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigLessThan(String value) {
            addCriterion("training_config <", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigLessThanOrEqualTo(String value) {
            addCriterion("training_config <=", value, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigIn(List<String> values) {
            addCriterion("training_config in", values, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigNotIn(List<String> values) {
            addCriterion("training_config not in", values, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigBetween(String value1, String value2) {
            addCriterion("training_config between", value1, value2, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingConfigNotBetween(String value1, String value2) {
            addCriterion("training_config not between", value1, value2, "trainingConfig");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportIsNull() {
            addCriterion("training_sample_export is null");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportIsNotNull() {
            addCriterion("training_sample_export is not null");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportEqualTo(String value) {
            addCriterion("training_sample_export =", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportNotEqualTo(String value) {
            addCriterion("training_sample_export <>", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportGreaterThan(String value) {
            addCriterion("training_sample_export >", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportGreaterThanOrEqualTo(String value) {
            addCriterion("training_sample_export >=", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportLessThan(String value) {
            addCriterion("training_sample_export <", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportLessThanOrEqualTo(String value) {
            addCriterion("training_sample_export <=", value, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportIn(List<String> values) {
            addCriterion("training_sample_export in", values, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportNotIn(List<String> values) {
            addCriterion("training_sample_export not in", values, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportBetween(String value1, String value2) {
            addCriterion("training_sample_export between", value1, value2, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainingSampleExportNotBetween(String value1, String value2) {
            addCriterion("training_sample_export not between", value1, value2, "trainingSampleExport");
            return (Criteria) this;
        }

        public Criteria andTrainStatusIsNull() {
            addCriterion("train_status is null");
            return (Criteria) this;
        }

        public Criteria andTrainStatusIsNotNull() {
            addCriterion("train_status is not null");
            return (Criteria) this;
        }

        public Criteria andTrainStatusEqualTo(String value) {
            addCriterion("train_status =", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusNotEqualTo(String value) {
            addCriterion("train_status <>", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusGreaterThan(String value) {
            addCriterion("train_status >", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusGreaterThanOrEqualTo(String value) {
            addCriterion("train_status >=", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusLessThan(String value) {
            addCriterion("train_status <", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusLessThanOrEqualTo(String value) {
            addCriterion("train_status <=", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusLike(String value) {
            addCriterion("train_status like", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusNotLike(String value) {
            addCriterion("train_status not like", value, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusIn(List<String> values) {
            addCriterion("train_status in", values, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusNotIn(List<String> values) {
            addCriterion("train_status not in", values, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusBetween(String value1, String value2) {
            addCriterion("train_status between", value1, value2, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStatusNotBetween(String value1, String value2) {
            addCriterion("train_status not between", value1, value2, "trainStatus");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeIsNull() {
            addCriterion("train_started_time is null");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeIsNotNull() {
            addCriterion("train_started_time is not null");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeEqualTo(Date value) {
            addCriterion("train_started_time =", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeNotEqualTo(Date value) {
            addCriterion("train_started_time <>", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeGreaterThan(Date value) {
            addCriterion("train_started_time >", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("train_started_time >=", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeLessThan(Date value) {
            addCriterion("train_started_time <", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeLessThanOrEqualTo(Date value) {
            addCriterion("train_started_time <=", value, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeIn(List<Date> values) {
            addCriterion("train_started_time in", values, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeNotIn(List<Date> values) {
            addCriterion("train_started_time not in", values, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeBetween(Date value1, Date value2) {
            addCriterion("train_started_time between", value1, value2, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainStartedTimeNotBetween(Date value1, Date value2) {
            addCriterion("train_started_time not between", value1, value2, "trainStartedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeIsNull() {
            addCriterion("train_completed_time is null");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeIsNotNull() {
            addCriterion("train_completed_time is not null");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeEqualTo(Date value) {
            addCriterion("train_completed_time =", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeNotEqualTo(Date value) {
            addCriterion("train_completed_time <>", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeGreaterThan(Date value) {
            addCriterion("train_completed_time >", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("train_completed_time >=", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeLessThan(Date value) {
            addCriterion("train_completed_time <", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeLessThanOrEqualTo(Date value) {
            addCriterion("train_completed_time <=", value, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeIn(List<Date> values) {
            addCriterion("train_completed_time in", values, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeNotIn(List<Date> values) {
            addCriterion("train_completed_time not in", values, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeBetween(Date value1, Date value2) {
            addCriterion("train_completed_time between", value1, value2, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andTrainCompletedTimeNotBetween(Date value1, Date value2) {
            addCriterion("train_completed_time not between", value1, value2, "trainCompletedTime");
            return (Criteria) this;
        }

        public Criteria andModelSceneIsNull() {
            addCriterion("model_scene is null");
            return (Criteria) this;
        }

        public Criteria andModelSceneIsNotNull() {
            addCriterion("model_scene is not null");
            return (Criteria) this;
        }

        public Criteria andModelSceneEqualTo(String value) {
            addCriterion("model_scene =", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneNotEqualTo(String value) {
            addCriterion("model_scene <>", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneGreaterThan(String value) {
            addCriterion("model_scene >", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneGreaterThanOrEqualTo(String value) {
            addCriterion("model_scene >=", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneLessThan(String value) {
            addCriterion("model_scene <", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneLessThanOrEqualTo(String value) {
            addCriterion("model_scene <=", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneLike(String value) {
            addCriterion("model_scene like", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneNotLike(String value) {
            addCriterion("model_scene not like", value, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneIn(List<String> values) {
            addCriterion("model_scene in", values, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneNotIn(List<String> values) {
            addCriterion("model_scene not in", values, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneBetween(String value1, String value2) {
            addCriterion("model_scene between", value1, value2, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelSceneNotBetween(String value1, String value2) {
            addCriterion("model_scene not between", value1, value2, "modelScene");
            return (Criteria) this;
        }

        public Criteria andModelVersionIsNull() {
            addCriterion("model_version is null");
            return (Criteria) this;
        }

        public Criteria andModelVersionIsNotNull() {
            addCriterion("model_version is not null");
            return (Criteria) this;
        }

        public Criteria andModelVersionEqualTo(String value) {
            addCriterion("model_version =", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotEqualTo(String value) {
            addCriterion("model_version <>", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionGreaterThan(String value) {
            addCriterion("model_version >", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionGreaterThanOrEqualTo(String value) {
            addCriterion("model_version >=", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLessThan(String value) {
            addCriterion("model_version <", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLessThanOrEqualTo(String value) {
            addCriterion("model_version <=", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionLike(String value) {
            addCriterion("model_version like", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotLike(String value) {
            addCriterion("model_version not like", value, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionIn(List<String> values) {
            addCriterion("model_version in", values, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotIn(List<String> values) {
            addCriterion("model_version not in", values, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionBetween(String value1, String value2) {
            addCriterion("model_version between", value1, value2, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelVersionNotBetween(String value1, String value2) {
            addCriterion("model_version not between", value1, value2, "modelVersion");
            return (Criteria) this;
        }

        public Criteria andModelFilePathIsNull() {
            addCriterion("model_file_path is null");
            return (Criteria) this;
        }

        public Criteria andModelFilePathIsNotNull() {
            addCriterion("model_file_path is not null");
            return (Criteria) this;
        }

        public Criteria andModelFilePathEqualTo(String value) {
            addCriterion("model_file_path =", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathNotEqualTo(String value) {
            addCriterion("model_file_path <>", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathGreaterThan(String value) {
            addCriterion("model_file_path >", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathGreaterThanOrEqualTo(String value) {
            addCriterion("model_file_path >=", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathLessThan(String value) {
            addCriterion("model_file_path <", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathLessThanOrEqualTo(String value) {
            addCriterion("model_file_path <=", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathLike(String value) {
            addCriterion("model_file_path like", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathNotLike(String value) {
            addCriterion("model_file_path not like", value, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathIn(List<String> values) {
            addCriterion("model_file_path in", values, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathNotIn(List<String> values) {
            addCriterion("model_file_path not in", values, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathBetween(String value1, String value2) {
            addCriterion("model_file_path between", value1, value2, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelFilePathNotBetween(String value1, String value2) {
            addCriterion("model_file_path not between", value1, value2, "modelFilePath");
            return (Criteria) this;
        }

        public Criteria andModelUrlIsNull() {
            addCriterion("model_url is null");
            return (Criteria) this;
        }

        public Criteria andModelUrlIsNotNull() {
            addCriterion("model_url is not null");
            return (Criteria) this;
        }

        public Criteria andModelUrlEqualTo(String value) {
            addCriterion("model_url =", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlNotEqualTo(String value) {
            addCriterion("model_url <>", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlGreaterThan(String value) {
            addCriterion("model_url >", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlGreaterThanOrEqualTo(String value) {
            addCriterion("model_url >=", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlLessThan(String value) {
            addCriterion("model_url <", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlLessThanOrEqualTo(String value) {
            addCriterion("model_url <=", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlLike(String value) {
            addCriterion("model_url like", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlNotLike(String value) {
            addCriterion("model_url not like", value, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlIn(List<String> values) {
            addCriterion("model_url in", values, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlNotIn(List<String> values) {
            addCriterion("model_url not in", values, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlBetween(String value1, String value2) {
            addCriterion("model_url between", value1, value2, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelUrlNotBetween(String value1, String value2) {
            addCriterion("model_url not between", value1, value2, "modelUrl");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsIsNull() {
            addCriterion("model_performance_metrics is null");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsIsNotNull() {
            addCriterion("model_performance_metrics is not null");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsEqualTo(String value) {
            addCriterion("model_performance_metrics =", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsNotEqualTo(String value) {
            addCriterion("model_performance_metrics <>", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsGreaterThan(String value) {
            addCriterion("model_performance_metrics >", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsGreaterThanOrEqualTo(String value) {
            addCriterion("model_performance_metrics >=", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsLessThan(String value) {
            addCriterion("model_performance_metrics <", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsLessThanOrEqualTo(String value) {
            addCriterion("model_performance_metrics <=", value, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsIn(List<String> values) {
            addCriterion("model_performance_metrics in", values, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsNotIn(List<String> values) {
            addCriterion("model_performance_metrics not in", values, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsBetween(String value1, String value2) {
            addCriterion("model_performance_metrics between", value1, value2, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andModelPerformanceMetricsNotBetween(String value1, String value2) {
            addCriterion("model_performance_metrics not between", value1, value2, "modelPerformanceMetrics");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}