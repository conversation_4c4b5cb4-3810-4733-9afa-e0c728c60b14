package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrainingSampleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public TrainingSampleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdIsNull() {
            addCriterion("sample_data_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdIsNotNull() {
            addCriterion("sample_data_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdEqualTo(Integer value) {
            addCriterion("sample_data_id =", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdNotEqualTo(Integer value) {
            addCriterion("sample_data_id <>", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdGreaterThan(Integer value) {
            addCriterion("sample_data_id >", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_data_id >=", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdLessThan(Integer value) {
            addCriterion("sample_data_id <", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdLessThanOrEqualTo(Integer value) {
            addCriterion("sample_data_id <=", value, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdIn(List<Integer> values) {
            addCriterion("sample_data_id in", values, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdNotIn(List<Integer> values) {
            addCriterion("sample_data_id not in", values, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdBetween(Integer value1, Integer value2) {
            addCriterion("sample_data_id between", value1, value2, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_data_id not between", value1, value2, "sampleDataId");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeIsNull() {
            addCriterion("sample_data_id_type is null");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeIsNotNull() {
            addCriterion("sample_data_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeEqualTo(String value) {
            addCriterion("sample_data_id_type =", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeNotEqualTo(String value) {
            addCriterion("sample_data_id_type <>", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeGreaterThan(String value) {
            addCriterion("sample_data_id_type >", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sample_data_id_type >=", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeLessThan(String value) {
            addCriterion("sample_data_id_type <", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeLessThanOrEqualTo(String value) {
            addCriterion("sample_data_id_type <=", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeLike(String value) {
            addCriterion("sample_data_id_type like", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeNotLike(String value) {
            addCriterion("sample_data_id_type not like", value, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeIn(List<String> values) {
            addCriterion("sample_data_id_type in", values, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeNotIn(List<String> values) {
            addCriterion("sample_data_id_type not in", values, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeBetween(String value1, String value2) {
            addCriterion("sample_data_id_type between", value1, value2, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleDataIdTypeNotBetween(String value1, String value2) {
            addCriterion("sample_data_id_type not between", value1, value2, "sampleDataIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdIsNull() {
            addCriterion("sample_caption_id is null");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdIsNotNull() {
            addCriterion("sample_caption_id is not null");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdEqualTo(Integer value) {
            addCriterion("sample_caption_id =", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdNotEqualTo(Integer value) {
            addCriterion("sample_caption_id <>", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdGreaterThan(Integer value) {
            addCriterion("sample_caption_id >", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("sample_caption_id >=", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdLessThan(Integer value) {
            addCriterion("sample_caption_id <", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdLessThanOrEqualTo(Integer value) {
            addCriterion("sample_caption_id <=", value, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdIn(List<Integer> values) {
            addCriterion("sample_caption_id in", values, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdNotIn(List<Integer> values) {
            addCriterion("sample_caption_id not in", values, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdBetween(Integer value1, Integer value2) {
            addCriterion("sample_caption_id between", value1, value2, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("sample_caption_id not between", value1, value2, "sampleCaptionId");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeIsNull() {
            addCriterion("sample_caption_id_type is null");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeIsNotNull() {
            addCriterion("sample_caption_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeEqualTo(String value) {
            addCriterion("sample_caption_id_type =", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeNotEqualTo(String value) {
            addCriterion("sample_caption_id_type <>", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeGreaterThan(String value) {
            addCriterion("sample_caption_id_type >", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sample_caption_id_type >=", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeLessThan(String value) {
            addCriterion("sample_caption_id_type <", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeLessThanOrEqualTo(String value) {
            addCriterion("sample_caption_id_type <=", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeLike(String value) {
            addCriterion("sample_caption_id_type like", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeNotLike(String value) {
            addCriterion("sample_caption_id_type not like", value, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeIn(List<String> values) {
            addCriterion("sample_caption_id_type in", values, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeNotIn(List<String> values) {
            addCriterion("sample_caption_id_type not in", values, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeBetween(String value1, String value2) {
            addCriterion("sample_caption_id_type between", value1, value2, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andSampleCaptionIdTypeNotBetween(String value1, String value2) {
            addCriterion("sample_caption_id_type not between", value1, value2, "sampleCaptionIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdIsNull() {
            addCriterion("related_training_id is null");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdIsNotNull() {
            addCriterion("related_training_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdEqualTo(Integer value) {
            addCriterion("related_training_id =", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdNotEqualTo(Integer value) {
            addCriterion("related_training_id <>", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdGreaterThan(Integer value) {
            addCriterion("related_training_id >", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("related_training_id >=", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdLessThan(Integer value) {
            addCriterion("related_training_id <", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdLessThanOrEqualTo(Integer value) {
            addCriterion("related_training_id <=", value, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdIn(List<Integer> values) {
            addCriterion("related_training_id in", values, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdNotIn(List<Integer> values) {
            addCriterion("related_training_id not in", values, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdBetween(Integer value1, Integer value2) {
            addCriterion("related_training_id between", value1, value2, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdNotBetween(Integer value1, Integer value2) {
            addCriterion("related_training_id not between", value1, value2, "relatedTrainingId");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeIsNull() {
            addCriterion("related_training_id_type is null");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeIsNotNull() {
            addCriterion("related_training_id_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeEqualTo(String value) {
            addCriterion("related_training_id_type =", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeNotEqualTo(String value) {
            addCriterion("related_training_id_type <>", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeGreaterThan(String value) {
            addCriterion("related_training_id_type >", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("related_training_id_type >=", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeLessThan(String value) {
            addCriterion("related_training_id_type <", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeLessThanOrEqualTo(String value) {
            addCriterion("related_training_id_type <=", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeLike(String value) {
            addCriterion("related_training_id_type like", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeNotLike(String value) {
            addCriterion("related_training_id_type not like", value, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeIn(List<String> values) {
            addCriterion("related_training_id_type in", values, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeNotIn(List<String> values) {
            addCriterion("related_training_id_type not in", values, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeBetween(String value1, String value2) {
            addCriterion("related_training_id_type between", value1, value2, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andRelatedTrainingIdTypeNotBetween(String value1, String value2) {
            addCriterion("related_training_id_type not between", value1, value2, "relatedTrainingIdType");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}