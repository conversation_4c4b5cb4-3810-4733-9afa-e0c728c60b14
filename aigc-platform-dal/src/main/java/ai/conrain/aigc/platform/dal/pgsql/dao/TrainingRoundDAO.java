package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.TrainingRoundExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TrainingRoundDAO {
    long countByExample(TrainingRoundExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TrainingRoundDO record);

    int insertSelective(TrainingRoundDO record);

    List<TrainingRoundDO> selectByExample(TrainingRoundExample example);

    TrainingRoundDO selectByPrimaryKey(Integer id);

    TrainingRoundDO lockByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TrainingRoundDO record, @Param("example") TrainingRoundExample example);

    int updateByExample(@Param("record") TrainingRoundDO record, @Param("example") TrainingRoundExample example);

    int updateByPrimaryKeySelective(TrainingRoundDO record);

    int updateByPrimaryKey(TrainingRoundDO record);
}