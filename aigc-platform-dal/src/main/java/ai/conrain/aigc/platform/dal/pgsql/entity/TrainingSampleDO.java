package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 训练样本表，存储训练使用的样本数据
 * 对应数据表：training_sample
 */
@Data
public class TrainingSampleDO implements Serializable {
    /**
     * 主键，自增
     */
    private Integer id;

    /**
     * 样本数据ID
     */
    private Integer sampleDataId;

    /**
     * 样本数据ID类型
     */
    private String sampleDataIdType;

    /**
     * 样本标注ID
     */
    private Integer sampleCaptionId;

    /**
     * 样本标注ID类型
     */
    private String sampleCaptionIdType;

    /**
     * 关联训练ID
     */
    private Integer relatedTrainingId;

    /**
     * 关联训练ID类型
     */
    private String relatedTrainingIdType;

    /**
     * 扩展信息字段
     */
    private String extInfo;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录最后修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}