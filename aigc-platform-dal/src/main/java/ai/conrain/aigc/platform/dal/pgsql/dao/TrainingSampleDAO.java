package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.TrainingSampleExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TrainingSampleDAO {
    long countByExample(TrainingSampleExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TrainingSampleDO record);

    int insertSelective(TrainingSampleDO record);

    List<TrainingSampleDO> selectByExample(TrainingSampleExample example);

    TrainingSampleDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TrainingSampleDO record, @Param("example") TrainingSampleExample example);

    int updateByExample(@Param("record") TrainingSampleDO record, @Param("example") TrainingSampleExample example);

    int updateByPrimaryKeySelective(TrainingSampleDO record);

    int updateByPrimaryKey(TrainingSampleDO record);
}