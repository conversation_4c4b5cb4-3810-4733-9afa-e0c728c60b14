package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ClothStyleCountDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageBatchCountDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCountDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ImageDAO {
    long countByExample(ImageExample example);

    List<ClothStyleCountDO> countClothStyle();

    List<ImageCountDO> batchCountByExample(ImageExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageDO record);

    int insertSelective(ImageDO record);

    List<ImageDO> selectByExample(ImageExample example);

    List<ImageDO> selectIdByExample(ImageExample example);

    ImageDO selectByPrimaryKey(Integer id);

    List<String> selectAllTags(ImageDO record);

    ImageDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageDO record, @Param("example") ImageExample example);

    int updateByExample(@Param("record") ImageDO record, @Param("example") ImageExample example);

    int updateByPrimaryKeySelective(ImageDO record);

    int updateByPrimaryKey(ImageDO record);

    int logicalDeleteByExample(@Param("example") ImageExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    /**
     * 统计 type 为 cloth 的图片中各个 intendedUse 流派的数量
     * @return 流派统计结果，key为流派名称，value为数量
     */
    @MapKey("intendedUse")
    List<Map<String, Object>> countByIntendedUse();

    //查询已经通过gemini flash完成打标，但不在image_caption进行索引的场景图片
    List<ImageDO> selectGeminiFlashCaptionedSceneImages4Embedding(@Param("limit") Integer limit);

    ImageDO getByHashOrPath(@Param("hash") String hash, @Param("path") String path);
}