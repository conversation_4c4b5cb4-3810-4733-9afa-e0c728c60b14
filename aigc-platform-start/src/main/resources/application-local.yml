logging:
  #日志目录
  path: ./logs
spring:
  datasource:
    # MySQL 数据源
    mysql:
      url: ************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: admin123

    # PostgreSQL 数据源
    pgsql:
      url: *************************************************************************************
      driver-class-name: org.postgresql.Driver
      username: postgres
      password:
  redis:
    host: localhost
    password: 123
  schedulerx2:
    enabled: false
    endpoint: acm.aliyun.com
    namespace: 1b41d844-f17b-4129-9080-367e65b5d321
    groupId: aigc-platform
    appKey: WWRiaWZ2D7OjUtjrFaTic4wA
  jackson:
    default-property-inclusion: NON_NULL
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
app:
  custom:
    tenant: MuseGate
    env: LOCAL
    domain: http://localhost:8080
aliyun:
  sts:
    endpoint: sts.cn-zhangjiakou.aliyuncs.com
  sms:
    accessKeyId: LTAI5tDoz5LzRafiN33aKkwj
    accessKeySecret: ******************************
    regionId: cn-zhangjiakou
    endpoint: dysmsapi.aliyuncs.com
    #    短信模板绑定的签名要和这里一致
    signName: 霖润智能
    mock: true
  oss:
    endpoint: https://oss-cn-zhangjiakou.aliyuncs.com
    bucket: aigc-platform-dev
  security:
    accessKeyId: LTAI5tKiqrJPS3ih4ynvheHq
    accessKeySecret: ******************************
    endpoint: green-cip.cn-hangzhou.aliyuncs.com
  imm:
    endpoint: imm.cn-zhangjiakou.aliyuncs.com

zlpay:
  url: https://ysczlpay.zlinepay.com/payGateway/api
  merchantNo: M230926211
  appId: 47a9912dd189440ea5c2c26f13bdd6ca
  merchantPriKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALZ0KihHpvvcPHJJX3NTc/sXCe113rHkNez5DP8BkBEo93OcclafcDsVj7A3N7WK/kQBFuGu5gsM9/UK/CQVs+0InKH5x1INb+eN4loyxLJjRavUSB+pXV6bzQX0oZSY/53L7Dwvi+wwl1+WUQXB5bYljaKFnEm2hfIkVV5VDUD3AgMBAAECgYBAPNIC8IdIMZhOnKqwjfdNtiTWqCNJ+pFJ5729oq04fXXyDGjtOqFnDAZVAnvovREcnE2UE+IGjgBXFBEGG2YHPDKb7DFopDSBqKeWiOqoD6ZGV0a0awEMg9hvIij6L1fyB0wtsMREnoSlHX3FQD7k5nNUWd1RYThq1d/AxUNHQQJBAOAKMJbLPDqX4eVFRvJK6jnz5cefTE2unTelXAk+sQqApgI1y5rF7iHL/lC+ADiRvsJGyzjVuBa0SMYOmV18z/ECQQDQe0aYsM8tNXaoIj5FizvxVOcHKsVFY1xloY/XGpd8zzv2YLX6tlsLkB2uXYloh8QdfP8jks8mVXdqoSPWhgdnAkBD+HNzXGeCc0/yigkTO01Clc9xt8+jhcVm+4EnwOBfilZTN1T1OPWdRVF715kjkHwqXuYK8TQvlzS4+fbnmRVRAkEAw9fKDY2qURG0EWV5keKlwKK9E+J9xhz8owwOD0cPn1MdROi8inPbOxV6jF2ZwvQmqnzNQCGdLqmCsEwHWnI1aQJAVyrvJTx2rtoLGQb/qB0bdLq8/TcmxUglgXmW4l3rQH+e2oMQMA42E9V4TixIYTFTDGWkc4NcJkLYegEjin77Aw==
  platPubKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAudc1lfbldbQTSpy1slQFL/Di5+wADyBmKsMxVhNemmg4M/xNEbrJXdaNI1WC4oy/OFYSGlg0MOOjwgFii++uKQ8wkjMDIAH4TAy/REazemq9zxt8JzZoJg2Bh0edUkN9bHF1DkJyTvLKLWQwaimW2GvakWcrsZrJuMmv2zxJW1C6eMNyXmbF+KKwmB0eWB/itZR6skQ2svip3hySHpS/RTXrqBWwc8Fvh9Vunm9DnAXV9dYEOrvCKMX8p4OoYk9e4wbrEKvjMFFHfwgZdOL07tuQK8YMP0zBfyFLWV6t9Ii3OpXJlLGQxErNIEWEXR1EhaLP/akCnGnDnGwoureFZQIDAQAB
  notifyUrl: https://kdd-dev.conrain.cn/notify/zlpay/result

ai:
  qwen:
    service: http://lq.lianqiai.cn:20310/
    sec: none
    timeout: 10
    model: QWen/QWen-14B-Chat
  gpt:
    service: https://api.openai.com/
    sec: ***************************************************
    timeout: 20
    model: gpt-4-1106-preview
  imageAnalysis:
    baseUrl: http://127.0.0.1:8815/
    apiKey: sk-0amBWPVUuFQiw3HM90M0FWJ5q8juA_zLMIKy_V46xj8
  styleImage:
    url: http://127.0.0.1:8821/process_image_data
  imageQuality:
    url: http://127.0.0.1:8821/
  training:
    baseUrl: http://127.0.0.1:8825
    fileServerUrl: http://127.0.0.1:8841
    apiKey: aBJjkbvkIGB^*&

comfyui:
  service:
    url: http://localhost:20312
  ws:
    url: ws://localhost:20312
  lora:
    url: http://localhost:20316
  output:
    path: /home/<USER>/aigc/ComfyUI/output/
  input:
    path: /home/<USER>/aigc/ComfyUI/input/
  file:
    4090url: http://localhost:20310
    a800url: http://localhost:20349
    embedWorkflow: true
    fileType: png
    needSyncLoraFile: false

# 微信支付相关参数
wx:
  pay:
    # 接收结果通知地址
    notifyUrl: https://aigc-dev.conrain.cn/wx/pay/notify

rocketmq:
  enabled: false
  name-server: 127.0.0.1:9876
  producer:
    group: aigc-platform-local-producer-group
  consumer:
    group: aigc-platform-local-consumer-group

label:
  service:
    url: http://127.0.0.1:8820
    token: sk-0amBWPVUuFQiw3HM90M0FWJ5q8juA_zLMIKy_V46xj8

# 美图AI服务配置
meitu:
  api:
    url: https://openapi.mtlab.meitu.com
    endpoints:
      removeWrinkle: /v1/mtrmwrink
  auth:
    appId: 358768
    appKey: ef5269070f0b4e01b833ab0698a53506
    secretId: 29c3894c83e8457e86d0ac1ef48120d0

# ffmpeg
ffmpeg:
  lib:
    path: libs/ffmpeg/ffmpeg-lite_mac/
  input:
    path: /tmp/ffmpeg/input/
  output:
    path: /tmp/ffmpeg/output/
